
import 'package:isar/isar.dart';

part 'user.g.dart';

@collection
class User {
  Id? isarId;
  final int id;
  final String name;
  final int age;
  final String email;
  final String? profileImageUrl;
  final String? bio;
  final DateTime joinDate;
  final int favoriteMoviesCount;

  User({
    required this.id,
    required this.name,
    required this.age,
    required this.email,
    this.profileImageUrl,
    this.bio,
    required this.joinDate,
    this.favoriteMoviesCount = 0,
  });
}
